/**
 * @fileoverview Valdi CLI Doctor Command - Environment Health Diagnostics
 *
 * This module implements the `valdi doctor` command, which performs comprehensive
 * health checks on the Valdi development environment. It validates system requirements,
 * tool installations, workspace configuration, and provides actionable feedback
 * for resolving issues.
 *
 * <AUTHOR>
 *
 * @example
 * ```bash
 * # Basic health check
 * valdi doctor
 *
 * # Detailed diagnostics with verbose output
 * valdi doctor --verbose
 *
 * # Machine-readable JSON output for CI/CD
 * valdi doctor --json
 *
 * # Attempt automatic fixes where possible
 * valdi doctor --fix
 * ```

 * @see {@link https://bazel.build/install} Bazel Installation Guide
 * @see {@link https://nodejs.org} Node.js Installation
 */

import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import type { Argv } from 'yargs';
import { ANSI_COLORS } from '../core/constants';
import type { ArgumentsResolver } from '../utils/ArgumentsResolver';
import { BazelClient } from '../utils/BazelClient';
import { checkCommandExists, runCliCommand } from '../utils/cliUtils';
import { makeCommandHandler } from '../utils/errorUtils';
import { wrapInColor } from '../utils/logUtils';

/**
 * Command line parameters for the doctor command.
 *
 * @interface CommandParameters
 */
interface CommandParameters {
  /** Enable detailed diagnostic information output */
  verbose: boolean;
  /** Attempt to automatically fix issues where possible */
  fix: boolean;
  /** Output results in JSON format for machine processing */
  json: boolean;
}

/**
 * Represents the result of a single diagnostic check.
 *
 * @interface DiagnosticResult
 */
interface DiagnosticResult {
  /** Human-readable name of the diagnostic check */
  name: string;
  /** Status of the check: pass (✓), warn (⚠), or fail (✗) */
  status: 'pass' | 'warn' | 'fail';
  /** Primary message describing the check result */
  message: string;
  /** Optional detailed information about the check */
  details?: string;
  /** Whether this issue can potentially be auto-fixed */
  fixable?: boolean;
  /** Command or instruction to fix the issue */
  fixCommand?: string;
}

/**
 * Main class responsible for performing Valdi environment health diagnostics.
 *
 * This class orchestrates various system checks to ensure the development environment
 * is properly configured for Valdi development. It validates:
 * - Node.js version compatibility (≥18.0.0)
 * - Bazel build system installation and functionality
 * - Valdi workspace structure and configuration
 * - Platform-specific development tools (Android SDK, Xcode)
 * - Required development dependencies (git, npm, watchman)
 *
 * @class ValdiDoctor
 */
class ValdiDoctor {
  /** Collection of diagnostic check results */
  private readonly results: DiagnosticResult[] = [];

  /** Whether to show detailed diagnostic information */
  private readonly verbose: boolean;

  /** Whether to attempt automatic fixes for detected issues */
  private readonly autoFix: boolean;

  /** Whether to output results in JSON format */
  private readonly jsonOutput: boolean;

  /**
   * Creates a new ValdiDoctor instance.
   *
   * @param verbose - Enable detailed diagnostic output
   * @param autoFix - Attempt to automatically fix issues where possible
   * @param jsonOutput - Output results in JSON format for machine processing
   *
   */
  constructor(verbose: boolean, autoFix: boolean, jsonOutput: boolean) {
    this.verbose = verbose;
    this.autoFix = autoFix;
    this.jsonOutput = jsonOutput;
  }

  /**
   * Executes all diagnostic checks in sequence.
   *
   * This method runs the complete suite of environment health checks:
   * 1. Node.js version validation
   * 2. Bazel installation and functionality
   * 3. Valdi workspace structure verification
   * 4. Platform-specific tool detection
   * 5. Development dependency validation
   *
   * @returns Promise that resolves when all diagnostics are complete
   
   *
   * @example
   * ```typescript
   * await doctor.runDiagnostics();
   * ```
   */
  async runDiagnostics(): Promise<void> {
    if (!this.jsonOutput) {
      console.log(wrapInColor('Running Valdi environment diagnostics...', ANSI_COLORS.BLUE_COLOR));
      console.log();
    }

    await this.checkNodeVersion();
    await this.checkBazelInstallation();
    this.checkWorkspaceStructure();
    await this.checkPlatformTools();
    await this.checkDependencies();
  }

  /**
   * Outputs the diagnostic results in the appropriate format.
   *
   * Depending on the configuration, this method will either:
   * - Output structured JSON for machine processing (--json flag)
   * - Display formatted, colored output for human consumption
   *
   
   *
   * @example
   * ```typescript
   * doctor.printResults(); // Human-readable output
   * ```
   */
  printResults(): void {
    if (this.jsonOutput) {
      this.printJsonResults();
    } else {
      this.printFormattedResults();
    }
  }

  /**
   * Adds a diagnostic result to the internal collection.
   *
   * @param result - The diagnostic result to add
   * @private
   
   */
  private addResult(result: DiagnosticResult): void {
    this.results.push(result);
  }

  /**
   * Attempts to automatically fix a detected issue.
   *
   * This method executes the provided fix command and reports the outcome.
   * It only runs when auto-fix mode is enabled and provides user feedback
   * about the success or failure of the fix attempt.
   *
   * @param tool - Name of the tool being fixed (for user feedback)
   * @param command - Shell command to execute for the fix
   * @returns Promise that resolves when the fix attempt is complete
   */
  private async attemptAutoFix(tool: string, command: string): Promise<void> {
    if (!this.autoFix) {
      return;
    }

    try {
      console.log(wrapInColor(`Attempting to fix ${tool}...`, ANSI_COLORS.YELLOW_COLOR));
      const { returnCode } = await runCliCommand(command);

      if (returnCode === 0) {
        console.log(wrapInColor(`✓ Successfully fixed ${tool}`, ANSI_COLORS.GREEN_COLOR));
      } else {
        console.log(wrapInColor(`✗ Failed to fix ${tool}`, ANSI_COLORS.RED_COLOR));
      }
    } catch (error) {
      console.log(wrapInColor(`✗ Failed to fix ${tool}: ${error instanceof Error ? error.message : 'Unknown error'}`, ANSI_COLORS.RED_COLOR));
    }
  }

  /**
   * Validates Node.js installation and version compatibility.
   *
   * Valdi requires Node.js version 18.0.0 or higher for optimal compatibility.
   * This method:
   * 1. Checks if Node.js is installed and accessible via PATH
   * 2. Validates the version meets minimum requirements (≥18.0.0)
   * 3. Optionally attempts to upgrade to Node.js 20 if auto-fix is enabled
   * 4. Provides specific installation/upgrade instructions
   *
   * @returns Promise that resolves when the check is complete
   */
  private async checkNodeVersion(): Promise<void> {
    try {
      const { stdout } = await runCliCommand('node --version');
      const version = stdout.trim();
      const versionParts = version.replace('v', '').split('.');
      const majorVersionStr = versionParts[0];

      if (!majorVersionStr) {
        throw new Error('Invalid version format');
      }

      const majorVersion = Number.parseInt(majorVersionStr, 10);

      if (majorVersion >= 18) {
        this.addResult({
          name: 'Node.js version',
          status: 'pass',
          message: `Node.js ${version} is installed`,
        });

        // Suggest upgrading to Node.js 20 for better performance
        if (this.autoFix && majorVersion < 20) {
          await this.attemptAutoFix('node', 'nvm install 20 && nvm use 20');
        }
      } else {
        this.addResult({
          name: 'Node.js version',
          status: 'fail',
          message: `Node.js ${version} is outdated. Valdi requires Node.js 18 or higher`,
          fixable: true,
          fixCommand: 'nvm install 18 && nvm use 18',
        });

        if (this.autoFix) {
          await this.attemptAutoFix('node', 'nvm install 18 && nvm use 18');
        }
      }
    } catch {
      this.addResult({
        name: 'Node.js version',
        status: 'fail',
        message: 'Node.js is not installed or not in PATH',
        fixable: true,
        fixCommand: 'Install Node.js from https://nodejs.org or use nvm',
      });
    }
  }

  /**
   * Validates Bazel build system installation and functionality.
   *
   * Bazel is the core build system for Valdi projects. This method:
   * 1. Attempts to create a BazelClient instance
   * 2. Executes `bazel version` to verify installation and functionality
   * 3. Parses version information for display
   * 4. Provides installation guidance if Bazel is missing or broken
   *
   * @returns Promise that resolves when the check is complete
   * @see {@link https://bazel.build/install} Bazel Installation Guide
   */
  private async checkBazelInstallation(): Promise<void> {
    try {
      const bazel = new BazelClient();
      const [returnCode, versionInfo, errorInfo] = await bazel.getVersion();

      if (returnCode === 0 && versionInfo) {
        const versionLine = versionInfo.split('\n')[0] || 'Unknown version';
        this.addResult({
          name: 'Bazel installation',
          status: 'pass',
          message: `Bazel is installed: ${versionLine}`,
        });
      } else {
        this.addResult({
          name: 'Bazel installation',
          status: 'fail',
          message: 'Bazel is installed but not working correctly',
          details: errorInfo || versionInfo || 'Unknown error',
        });
      }
    } catch {
      this.addResult({
        name: 'Bazel installation',
        status: 'fail',
        message: 'Bazel is not installed or not in PATH',
        fixable: true,
        fixCommand: 'Install Bazel from https://bazel.build/install',
      });
    }
  }

  /**
   * Validates Valdi workspace structure and configuration files.
   *
   * **WORKSPACE File Requirement:**
   * Every Valdi application requires a WORKSPACE file at the project root. This file:
   * - Defines the Bazel workspace name
   * - Imports the Valdi framework as an external dependency
   * - Configures build rules and toolchains for Valdi development
   * - Is automatically created by `valdi bootstrap` when starting a new project
   *
   * **Configuration Files:**
   * - WORKSPACE file (required for all Valdi apps)
   * - .bazelrc file (recommended for build optimization and consistency)
   *
   * This method checks the current working directory for these essential files
   * and provides guidance if they're missing.
   *
   * @private
   * @since 1.0.0
   *
   * @see {@link https://bazel.build/concepts/build-ref#workspace} Bazel Workspace Documentation
   *
   * @example
   * ```typescript
   * this.checkWorkspaceStructure();
   * // Results in diagnostic output like:
   * // ✓ Valid Valdi workspace detected
   * // ✗ Not in a Valdi workspace directory
   * ```
   */
  private checkWorkspaceStructure(): void {
    const workspaceFile = path.join(process.cwd(), 'WORKSPACE');
    const bazelrcFile = path.join(process.cwd(), '.bazelrc');

    if (fs.existsSync(workspaceFile)) {
      this.addResult({
        name: 'Valdi workspace',
        status: 'pass',
        message: 'Valid Valdi workspace detected',
      });
    } else {
      this.addResult({
        name: 'Valdi workspace',
        status: 'fail',
        message: 'Not in a Valdi workspace directory',
        details: 'WORKSPACE file is required for all Valdi applications. Run `valdi bootstrap` to create a new project or navigate to an existing Valdi project root.',
        fixable: true,
        fixCommand: 'valdi bootstrap',
      });
    }

    if (fs.existsSync(bazelrcFile)) {
      this.addResult({
        name: 'Bazel configuration',
        status: 'pass',
        message: '.bazelrc file found',
      });
    } else {
      this.addResult({
        name: 'Bazel configuration',
        status: 'warn',
        message: '.bazelrc file not found',
        details: 'A .bazelrc file provides build optimization and consistency. Consider creating one or use `valdi bootstrap` for new projects.',
        fixable: true,
        fixCommand: 'Create .bazelrc file with Valdi-specific build configurations',
      });
    }
  }

  /**
   * Validates platform-specific development tools.
   *
   * Checks for the presence and configuration of platform-specific tools
   * required for mobile development:
   *
   * **Android Development:**
   * - Android SDK installation (via ANDROID_HOME or ANDROID_SDK_ROOT)
   * - Validates SDK directory exists and is accessible
   *
   * **iOS Development (macOS only):**
   * - Xcode installation and command line tools
   * - Validates Xcode developer directory is properly configured
   *
   * @returns Promise that resolves when all platform checks are complete
   */
  private async checkPlatformTools(): Promise<void> {
    // Check Android SDK
    const androidHome = process.env['ANDROID_HOME'] || process.env['ANDROID_SDK_ROOT'];
    if (androidHome && fs.existsSync(androidHome)) {
      this.addResult({
        name: 'Android SDK',
        status: 'pass',
        message: `Android SDK found at ${androidHome}`,
      });
    } else {
      this.addResult({
        name: 'Android SDK',
        status: 'warn',
        message: 'Android SDK not found',
        details: 'Set ANDROID_HOME environment variable for Android development',
      });
    }

    // Check Xcode (macOS only)
    if (os.platform() === 'darwin') {
      try {
        const { stdout } = await runCliCommand('xcode-select -p');
        this.addResult({
          name: 'Xcode',
          status: 'pass',
          message: `Xcode found at ${stdout.trim()}`,
        });
      } catch {
        this.addResult({
          name: 'Xcode',
          status: 'warn',
          message: 'Xcode not found or not properly configured',
          fixable: true,
          fixCommand: 'xcode-select --install',
        });
      }
    }
  }

  /**
   * Validates essential development dependencies.
   *
   * Checks for the installation and accessibility of core development tools:
   *
   * **Required Dependencies:**
   * - **git**: Version control system (critical for development)
   * - **npm**: Node.js package manager (critical for dependency management)
   *
   * **Optional Dependencies:**
   * - **watchman**: File watching service (recommended for development efficiency)
   *
   * For each dependency, this method:
   * 1. Checks if the command exists in PATH
   * 2. Attempts to retrieve version information
   * 3. Provides platform-specific installation instructions
   * 4. Optionally attempts auto-fix for critical dependencies
   *
   * @returns Promise that resolves when all dependency checks are complete
   * @private
   */
  private async checkDependencies(): Promise<void> {
    const dependencies = ['git', 'npm', 'watchman'];

    for (const dep of dependencies) {
      if (checkCommandExists(dep)) {
        try {
          const { stdout } = await runCliCommand(`${dep} --version`);
          const versionLine = stdout.split('\n')[0] || 'Unknown version';
          this.addResult({
            name: `${dep} installation`,
            status: 'pass',
            message: `${dep} is installed: ${versionLine}`,
          });
        } catch {
          this.addResult({
            name: `${dep} installation`,
            status: 'pass',
            message: `${dep} is installed`,
          });
        }
      } else {
        const fixCommand = this.getFixCommandForDependency(dep);
        this.addResult({
          name: `${dep} installation`,
          status: dep === 'watchman' ? 'warn' : 'fail',
          message: `${dep} is not installed or not in PATH`,
          fixable: true,
          fixCommand,
        });

        if (this.autoFix && dep !== 'watchman') {
          await this.attemptAutoFix(dep, fixCommand);
        }
      }
    }
  }

  /**
   * Generates platform-specific fix commands for missing dependencies.
   *
   * Provides appropriate installation commands based on the current platform
   * and the specific dependency that's missing.
   *
   * @param dep - The name of the missing dependency
   * @returns Platform-appropriate installation command or instruction
   */
  private getFixCommandForDependency(dep: string): string {
    switch (dep) {
      case 'git': {
        return os.platform() === 'darwin' ? 'brew install git' : 'Install Git from https://git-scm.com';
      }
      case 'npm': {
        return 'Install Node.js from https://nodejs.org (includes npm)';
      }
      case 'watchman': {
        return os.platform() === 'darwin' ? 'brew install watchman' : 'Install Watchman from https://facebook.github.io/watchman';
      }
      default: {
        return `Install ${dep}`;
      }
    }
  }

  /**
   * Outputs diagnostic results in JSON format for machine processing.
   *
   * Generates a structured JSON report containing:
   * - ISO timestamp of the diagnostic run
   * - Summary statistics (passed, warnings, failed, total)
   * - Complete array of diagnostic results with all details
   *
   * This format is ideal for:
   * - CI/CD pipeline integration
   * - Automated monitoring and alerting
   * - Programmatic analysis of environment health
   * @example
   * ```json
   * {
   *   "timestamp": "2024-01-15T10:30:00.000Z",
   *   "summary": { "passed": 8, "warnings": 0, "failed": 1, "total": 9 },
   *   "results": [...]
   * }
   * ```
   */
  private printJsonResults(): void {
    const passCount = this.results.filter(r => r.status === 'pass').length;
    const warnCount = this.results.filter(r => r.status === 'warn').length;
    const failCount = this.results.filter(r => r.status === 'fail').length;

    const output = {
      timestamp: new Date().toISOString(),
      summary: {
        passed: passCount,
        warnings: warnCount,
        failed: failCount,
        total: this.results.length,
      },
      results: this.results,
    };

    console.log(JSON.stringify(output, null, 2));
  }

  /**
   * Outputs diagnostic results in human-readable format with colors and icons.
   *
   * Generates a formatted report with:
   * - Colored status icons (✓ ⚠ ✗) for visual clarity
   * - Detailed messages for each diagnostic check
   * - Optional verbose details when requested
   * - Actionable fix commands for failed checks
   * - Summary statistics and overall health assessment
   *
   * The output uses ANSI color codes for enhanced readability:
   * - Green (✓): Successful checks
   * - Yellow (⚠): Warnings that don't block development
   * - Red (✗): Critical failures requiring attention
   * @example
   * ```
   * Valdi Doctor Report
   * ==================================================
   * ✓ Node.js version: Node.js v18.17.0 is installed
   * ✗ Bazel installation: Bazel is not installed or not in PATH
   *   Fix: Install Bazel from https://bazel.build/install
   * ==================================================
   * Summary: 1 passed, 0 warnings, 1 failed
   * ```
   */
  private printFormattedResults(): void {
    console.log(wrapInColor('Valdi Doctor Report', ANSI_COLORS.BLUE_COLOR));
    console.log('='.repeat(50));
    console.log();

    let passCount = 0;
    let warnCount = 0;
    let failCount = 0;

    for (const result of this.results) {
      const statusIcon = result.status === 'pass' ? '✓' : result.status === 'warn' ? '⚠' : '✗';
      const statusColor = result.status === 'pass' ? ANSI_COLORS.GREEN_COLOR :
                         result.status === 'warn' ? ANSI_COLORS.YELLOW_COLOR : ANSI_COLORS.RED_COLOR;

      console.log(`${wrapInColor(statusIcon, statusColor)} ${result.name}: ${result.message}`);

      if (this.verbose && result.details) {
        console.log(`  ${wrapInColor('Details:', ANSI_COLORS.GRAY_COLOR)} ${result.details}`);
      }

      if (result.fixable && result.fixCommand) {
        console.log(`  ${wrapInColor('Fix:', ANSI_COLORS.BLUE_COLOR)} ${result.fixCommand}`);
      }

      console.log();

      if (result.status === 'pass') passCount++;
      else if (result.status === 'warn') warnCount++;
      else failCount++;
    }

    console.log('='.repeat(50));
    console.log(`${wrapInColor('Summary:', ANSI_COLORS.BLUE_COLOR)} ${passCount} passed, ${warnCount} warnings, ${failCount} failed`);

    if (failCount > 0) {
      console.log();
      console.log(wrapInColor('Some issues need to be resolved before Valdi can work properly.', ANSI_COLORS.RED_COLOR));
    } else if (warnCount > 0) {
      console.log();
      console.log(wrapInColor('Your environment is mostly ready, but some optional tools are missing.', ANSI_COLORS.YELLOW_COLOR));
    } else {
      console.log();
      console.log(wrapInColor('Your Valdi development environment is ready! 🎉', ANSI_COLORS.GREEN_COLOR));
    }
  }
}

/**
 * Main entry point for the Valdi doctor command.
 *
 * This function serves as the command handler that:
 * 1. Extracts command line arguments (verbose, fix, json)
 * 2. Creates a new ValdiDoctor instance with the specified configuration
 * 3. Executes the complete diagnostic suite
 * 4. Outputs results in the requested format
 *
 * @param argv - Resolved command line arguments
 * @returns Promise that resolves when the doctor command completes
 * 
 * @example
 * ```bash
 * valdi doctor --verbose --fix --json
 * ```
 */
async function valdiDoctor(argv: ArgumentsResolver<CommandParameters>): Promise<void> {
  const verbose = argv.getArgument('verbose');
  const autoFix = argv.getArgument('fix');
  const jsonOutput = argv.getArgument('json');

  const doctor = new ValdiDoctor(verbose, autoFix, jsonOutput);
  await doctor.runDiagnostics();
  doctor.printResults();
}

// ============================================================================
// YARGS COMMAND CONFIGURATION
// ============================================================================

/**
 * The command name as it appears in the CLI.
 
 */
export const command = 'doctor';

/**
 * Human-readable description of the command for help output.
 
 */
export const describe = 'Check your Valdi development environment for common issues';

/**
 * Configures command line options and their validation.
 *
 * Defines three main options:
 * - `--verbose` (-v): Enable detailed diagnostic output
 * - `--fix` (-f): Attempt automatic fixes where possible
 * - `--json` (-j): Output results in JSON format for automation
 *
 * @param yargs - The yargs instance to configure
 
 */
export const builder = (yargs: Argv<CommandParameters>): void => {
  yargs
    .option('verbose', {
      describe: 'Show detailed diagnostic information',
      type: 'boolean',
      default: false,
      alias: 'v',
    })
    .option('fix', {
      describe: 'Attempt to automatically fix issues where possible',
      type: 'boolean',
      default: false,
      alias: 'f',
    })
    .option('json', {
      describe: 'Output results in JSON format',
      type: 'boolean',
      default: false,
      alias: 'j',
    });
};

/**
 * The command handler wrapped with error handling and logging.
 
 */
export const handler = makeCommandHandler(valdiDoctor);
