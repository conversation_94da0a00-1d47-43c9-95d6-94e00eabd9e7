import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import type { Argv } from 'yargs';
import { ANSI_COLORS } from '../core/constants';
import type { ArgumentsResolver } from '../utils/ArgumentsResolver';
import { BazelClient } from '../utils/BazelClient';
import { checkCommandExists, runCliCommand } from '../utils/cliUtils';
import { makeCommandHandler } from '../utils/errorUtils';
import { wrapInColor } from '../utils/logUtils';

interface CommandParameters {
  verbose: boolean;
  fix: boolean;
  json: boolean;
}

interface DiagnosticResult {
  name: string;
  status: 'pass' | 'warn' | 'fail';
  message: string;
  details?: string;
  fixable?: boolean;
  fixCommand?: string;
}

class ValdiDoctor {
  private results: DiagnosticResult[] = [];
  private verbose: boolean;
  private autoFix: boolean;
  private jsonOutput: boolean;

  constructor(verbose: boolean, autoFix: boolean, jsonOutput: boolean) {
    this.verbose = verbose;
    this.autoFix = autoFix;
    this.jsonOutput = jsonOutput;
  }

  private addResult(result: DiagnosticResult) {
    this.results.push(result);
  }

  private async attemptAutoFix(tool: string, command: string): Promise<void> {
    if (!this.autoFix) {
      return;
    }

    try {
      console.log(wrapInColor(`Attempting to fix ${tool}...`, ANSI_COLORS.YELLOW_COLOR));
      const { returnCode } = await runCliCommand(command);

      if (returnCode === 0) {
        console.log(wrapInColor(`✓ Successfully fixed ${tool}`, ANSI_COLORS.GREEN_COLOR));
      } else {
        console.log(wrapInColor(`✗ Failed to fix ${tool}`, ANSI_COLORS.RED_COLOR));
      }
    } catch (error) {
      console.log(wrapInColor(`✗ Failed to fix ${tool}: ${error instanceof Error ? error.message : 'Unknown error'}`, ANSI_COLORS.RED_COLOR));
    }
  }

  private async checkNodeVersion(): Promise<void> {
    try {
      const { stdout } = await runCliCommand('node --version');
      const version = stdout.trim();
      const versionParts = version.replace('v', '').split('.');
      const majorVersionStr = versionParts[0];

      if (!majorVersionStr) {
        throw new Error('Invalid version format');
      }

      const majorVersion = Number.parseInt(majorVersionStr, 10);

      if (majorVersion >= 18) {
        this.addResult({
          name: 'Node.js version',
          status: 'pass',
          message: `Node.js ${version} is installed`,
        });

        if (this.autoFix && majorVersion < 20) {
          await this.attemptAutoFix('node', 'nvm install 20 && nvm use 20');
        }
      } else {
        this.addResult({
          name: 'Node.js version',
          status: 'fail',
          message: `Node.js ${version} is outdated. Valdi requires Node.js 18 or higher`,
          fixable: true,
          fixCommand: 'nvm install 18 && nvm use 18',
        });

        if (this.autoFix) {
          await this.attemptAutoFix('node', 'nvm install 18 && nvm use 18');
        }
      }
    } catch {
      this.addResult({
        name: 'Node.js version',
        status: 'fail',
        message: 'Node.js is not installed or not in PATH',
        fixable: true,
        fixCommand: 'Install Node.js from https://nodejs.org or use nvm',
      });
    }
  }

  private async checkBazelInstallation(): Promise<void> {
    try {
      const bazel = new BazelClient();
      const [returnCode, versionInfo, errorInfo] = await bazel.getVersion();

      if (returnCode === 0 && versionInfo) {
        const versionLine = versionInfo.split('\n')[0] || 'Unknown version';
        this.addResult({
          name: 'Bazel installation',
          status: 'pass',
          message: `Bazel is installed: ${versionLine}`,
        });
      } else {
        this.addResult({
          name: 'Bazel installation',
          status: 'fail',
          message: 'Bazel is installed but not working correctly',
          details: errorInfo || versionInfo || 'Unknown error',
        });
      }
    } catch {
      this.addResult({
        name: 'Bazel installation',
        status: 'fail',
        message: 'Bazel is not installed or not in PATH',
        fixable: true,
        fixCommand: 'Install Bazel from https://bazel.build/install',
      });
    }
  }

  private checkWorkspaceStructure(): void {
    const workspaceFile = path.join(process.cwd(), 'WORKSPACE');
    const bazelrcFile = path.join(process.cwd(), '.bazelrc');

    if (fs.existsSync(workspaceFile)) {
      this.addResult({
        name: 'Valdi workspace',
        status: 'pass',
        message: 'Valid Valdi workspace detected',
      });
    } else {
      this.addResult({
        name: 'Valdi workspace',
        status: 'warn',
        message: 'Not in a Valdi workspace directory',
        details: 'Run this command from the root of a Valdi project',
      });
    }

    if (fs.existsSync(bazelrcFile)) {
      this.addResult({
        name: 'Bazel configuration',
        status: 'pass',
        message: '.bazelrc file found',
      });
    } else {
      this.addResult({
        name: 'Bazel configuration',
        status: 'warn',
        message: '.bazelrc file not found',
        details: 'Consider creating a .bazelrc file for build optimization',
      });
    }
  }

  private async checkPlatformTools(): Promise<void> {
    // Check Android SDK
    const androidHome = process.env['ANDROID_HOME'] || process.env['ANDROID_SDK_ROOT'];
    if (androidHome && fs.existsSync(androidHome)) {
      this.addResult({
        name: 'Android SDK',
        status: 'pass',
        message: `Android SDK found at ${androidHome}`,
      });
    } else {
      this.addResult({
        name: 'Android SDK',
        status: 'warn',
        message: 'Android SDK not found',
        details: 'Set ANDROID_HOME environment variable for Android development',
      });
    }

    // Check Xcode (macOS only)
    if (os.platform() === 'darwin') {
      try {
        const { stdout } = await runCliCommand('xcode-select -p');
        this.addResult({
          name: 'Xcode',
          status: 'pass',
          message: `Xcode found at ${stdout.trim()}`,
        });
      } catch {
        this.addResult({
          name: 'Xcode',
          status: 'warn',
          message: 'Xcode not found or not properly configured',
          fixable: true,
          fixCommand: 'xcode-select --install',
        });
      }
    }
  }

  private async checkDependencies(): Promise<void> {
    const dependencies = ['git', 'npm', 'watchman'];

    for (const dep of dependencies) {
      if (checkCommandExists(dep)) {
        try {
          const { stdout } = await runCliCommand(`${dep} --version`);
          const versionLine = stdout.split('\n')[0] || 'Unknown version';
          this.addResult({
            name: `${dep} installation`,
            status: 'pass',
            message: `${dep} is installed: ${versionLine}`,
          });
        } catch {
          this.addResult({
            name: `${dep} installation`,
            status: 'pass',
            message: `${dep} is installed`,
          });
        }
      } else {
        const fixCommand = this.getFixCommandForDependency(dep);
        this.addResult({
          name: `${dep} installation`,
          status: dep === 'watchman' ? 'warn' : 'fail',
          message: `${dep} is not installed or not in PATH`,
          fixable: true,
          fixCommand,
        });

        if (this.autoFix && dep !== 'watchman') {
          await this.attemptAutoFix(dep, fixCommand);
        }
      }
    }
  }

  private getFixCommandForDependency(dep: string): string {
    switch (dep) {
      case 'git': {
        return os.platform() === 'darwin' ? 'brew install git' : 'Install Git from https://git-scm.com';
      }
      case 'npm': {
        return 'Install Node.js from https://nodejs.org (includes npm)';
      }
      case 'watchman': {
        return os.platform() === 'darwin' ? 'brew install watchman' : 'Install Watchman from https://facebook.github.io/watchman';
      }
      default: {
        return `Install ${dep}`;
      }
    }
  }

  async runDiagnostics(): Promise<void> {
    if (!this.jsonOutput) {
      console.log(wrapInColor('Running Valdi environment diagnostics...', ANSI_COLORS.BLUE_COLOR));
      console.log();
    }

    await this.checkNodeVersion();
    await this.checkBazelInstallation();
    this.checkWorkspaceStructure();
    await this.checkPlatformTools();
    await this.checkDependencies();
  }

  printResults(): void {
    if (this.jsonOutput) {
      this.printJsonResults();
    } else {
      this.printFormattedResults();
    }
  }

  private printJsonResults(): void {
    const passCount = this.results.filter(r => r.status === 'pass').length;
    const warnCount = this.results.filter(r => r.status === 'warn').length;
    const failCount = this.results.filter(r => r.status === 'fail').length;

    const output = {
      timestamp: new Date().toISOString(),
      summary: {
        passed: passCount,
        warnings: warnCount,
        failed: failCount,
        total: this.results.length,
      },
      results: this.results,
    };

    console.log(JSON.stringify(output, null, 2));
  }

  private printFormattedResults(): void {
    console.log(wrapInColor('Valdi Doctor Report', ANSI_COLORS.BLUE_COLOR));
    console.log('='.repeat(50));
    console.log();

    let passCount = 0;
    let warnCount = 0;
    let failCount = 0;

    for (const result of this.results) {
      const statusIcon = result.status === 'pass' ? '✓' : result.status === 'warn' ? '⚠' : '✗';
      const statusColor = result.status === 'pass' ? ANSI_COLORS.GREEN_COLOR :
                         result.status === 'warn' ? ANSI_COLORS.YELLOW_COLOR : ANSI_COLORS.RED_COLOR;

      console.log(`${wrapInColor(statusIcon, statusColor)} ${result.name}: ${result.message}`);

      if (this.verbose && result.details) {
        console.log(`  ${wrapInColor('Details:', ANSI_COLORS.GRAY_COLOR)} ${result.details}`);
      }

      if (result.fixable && result.fixCommand) {
        console.log(`  ${wrapInColor('Fix:', ANSI_COLORS.BLUE_COLOR)} ${result.fixCommand}`);
      }

      console.log();

      if (result.status === 'pass') passCount++;
      else if (result.status === 'warn') warnCount++;
      else failCount++;
    }

    console.log('='.repeat(50));
    console.log(`${wrapInColor('Summary:', ANSI_COLORS.BLUE_COLOR)} ${passCount} passed, ${warnCount} warnings, ${failCount} failed`);

    if (failCount > 0) {
      console.log();
      console.log(wrapInColor('Some issues need to be resolved before Valdi can work properly.', ANSI_COLORS.RED_COLOR));
    } else if (warnCount > 0) {
      console.log();
      console.log(wrapInColor('Your environment is mostly ready, but some optional tools are missing.', ANSI_COLORS.YELLOW_COLOR));
    } else {
      console.log();
      console.log(wrapInColor('Your Valdi development environment is ready! 🎉', ANSI_COLORS.GREEN_COLOR));
    }
  }
}

async function valdiDoctor(argv: ArgumentsResolver<CommandParameters>): Promise<void> {
  const verbose = argv.getArgument('verbose');
  const autoFix = argv.getArgument('fix');
  const jsonOutput = argv.getArgument('json');

  const doctor = new ValdiDoctor(verbose, autoFix, jsonOutput);
  await doctor.runDiagnostics();
  doctor.printResults();
}

export const command = 'doctor';
export const describe = 'Check your Valdi development environment for common issues';
export const builder = (yargs: Argv<CommandParameters>) => {
  yargs
    .option('verbose', {
      describe: 'Show detailed diagnostic information',
      type: 'boolean',
      default: false,
      alias: 'v',
    })
    .option('fix', {
      describe: 'Attempt to automatically fix issues where possible',
      type: 'boolean',
      default: false,
      alias: 'f',
    })
    .option('json', {
      describe: 'Output results in JSON format',
      type: 'boolean',
      default: false,
      alias: 'j',
    });
};
export const handler = makeCommandHandler(valdiDoctor);
